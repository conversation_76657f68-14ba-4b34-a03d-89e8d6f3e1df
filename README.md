# X-UI Panel Automated Login Script

A clean, minimal Python script for automated login to X-UI panels with multi-threading support and enhanced user experience.

## Features

- 🚀 **Multi-threaded login attempts** for faster processing
- 🎯 **Clean, minimal code** with comprehensive error handling
- 📊 **Enhanced user experience** with real-time progress and summary
- ⚙️ **Configurable settings** (workers, timeout, etc.)
- 📁 **JSON configuration** for easy panel management
- 🔒 **Session management** with cookie extraction
- 📈 **Performance metrics** (response times)

## Installation

1. Clone or download the script
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Quick Start

1. Create a configuration file:
   ```bash
   python xui_login_automation.py --create-config panels.json
   ```

2. Edit `panels.json` with your actual panel details:
   ```json
   {
     "panels": [
       {
         "name": "Main Panel",
         "url": "https://your-panel.example.com",
         "username": "admin",
         "password": "your-password"
       }
     ]
   }
   ```

3. Run the script:
   ```bash
   python xui_login_automation.py -c panels.json
   ```

### Advanced Usage

```bash
# Use 10 worker threads with 15-second timeout
python xui_login_automation.py -c panels.json -w 10 -t 15

# Create config with custom filename
python xui_login_automation.py --create-config my-panels.json
```

### Command Line Options

- `-c, --config`: Configuration file path (default: panels.json)
- `-w, --workers`: Number of worker threads (default: 5)
- `-t, --timeout`: Request timeout in seconds (default: 10)
- `--create-config`: Create a sample configuration file

## Configuration Format

```json
{
  "panels": [
    {
      "name": "Panel Display Name",
      "url": "https://panel-url.com",
      "username": "admin",
      "password": "password123"
    }
  ]
}
```

## Output Example

```
🚀 Starting login attempts for 3 panels...
⚙️  Using 3 worker threads
------------------------------------------------------------
✅ SUCCESS | Main Panel              |   1.23s | Login successful
❌ FAILED  | Backup Panel            |   5.67s | HTTP 401: Unauthorized
✅ SUCCESS | Test Panel              |   2.34s | Login successful
------------------------------------------------------------
📊 SUMMARY: 2/3 successful logins

✅ Successful logins (2):
   • Main Panel (1.23s)
   • Test Panel (2.34s)

❌ Failed logins (1):
   • Backup Panel: HTTP 401: Unauthorized
```

## Security Notes

- Store configuration files securely
- Use environment variables for sensitive credentials in production
- Consider using encrypted configuration files for enhanced security

## Error Handling

The script handles various error conditions:
- Connection timeouts
- Network errors
- Invalid credentials
- Malformed responses
- Server errors

## License

This script is provided as-is for educational and automation purposes.
