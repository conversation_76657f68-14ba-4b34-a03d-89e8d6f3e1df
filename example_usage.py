#!/usr/bin/env python3
"""
Example usage of X-UI Login Automation
Demonstrates programmatic usage of the XUILoginAutomator class.
"""

from xui_login_automation import XUILoginAutomator, PanelConfig
import os


def example_basic_usage():
    """Basic usage example with hardcoded configurations."""
    print("🔧 Basic Usage Example")
    print("-" * 30)
    
    # Create panel configurations
    configs = [
        PanelConfig(
            url="https://panel1.example.com",
            username="admin",
            password="password123",
            name="Main Panel"
        ),
        PanelConfig(
            url="https://panel2.example.com",
            username="admin",
            password="password456",
            name="Backup Panel"
        )
    ]
    
    # Initialize automator
    automator = XUILoginAutomator(max_workers=5, timeout=10)
    
    # Perform login attempts
    results = automator.login_multiple(configs)
    
    # Print summary
    automator.print_summary()
    
    return results


def example_environment_variables():
    """Example using environment variables for credentials."""
    print("\n🌍 Environment Variables Example")
    print("-" * 35)
    
    # Get credentials from environment variables
    panels_data = [
        {
            'url': os.getenv('XUI_PANEL1_URL', 'https://panel1.example.com'),
            'username': os.getenv('XUI_PANEL1_USER', 'admin'),
            'password': os.getenv('XUI_PANEL1_PASS', 'default_pass'),
            'name': 'Panel 1 (from env)'
        },
        {
            'url': os.getenv('XUI_PANEL2_URL', 'https://panel2.example.com'),
            'username': os.getenv('XUI_PANEL2_USER', 'admin'),
            'password': os.getenv('XUI_PANEL2_PASS', 'default_pass'),
            'name': 'Panel 2 (from env)'
        }
    ]
    
    # Create configurations
    configs = [PanelConfig(**data) for data in panels_data]
    
    # Run with custom settings
    automator = XUILoginAutomator(max_workers=3, timeout=15)
    results = automator.login_multiple(configs)
    
    return results


def example_single_panel():
    """Example for testing a single panel."""
    print("\n🎯 Single Panel Example")
    print("-" * 25)
    
    config = PanelConfig(
        url="https://demo.example.com",
        username="demo_user",
        password="demo_pass",
        name="Demo Panel"
    )
    
    automator = XUILoginAutomator(max_workers=1, timeout=5)
    result = automator.login_to_panel(config)
    
    print(f"Panel: {result['name']}")
    print(f"Status: {'✅ Success' if result['success'] else '❌ Failed'}")
    print(f"Message: {result['message']}")
    print(f"Response Time: {result['response_time']}s")
    
    return result


def example_custom_processing():
    """Example with custom result processing."""
    print("\n⚙️ Custom Processing Example")
    print("-" * 30)
    
    configs = [
        PanelConfig("https://test1.com", "admin", "pass1", "Test 1"),
        PanelConfig("https://test2.com", "admin", "pass2", "Test 2"),
        PanelConfig("https://test3.com", "admin", "pass3", "Test 3")
    ]
    
    automator = XUILoginAutomator(max_workers=2, timeout=8)
    results = automator.login_multiple(configs)
    
    # Custom processing of results
    successful_panels = [r for r in results if r['success']]
    failed_panels = [r for r in results if not r['success']]
    
    print(f"\n📊 Custom Analysis:")
    print(f"   Total panels: {len(results)}")
    print(f"   Success rate: {len(successful_panels)/len(results)*100:.1f}%")
    
    if successful_panels:
        avg_response_time = sum(r['response_time'] for r in successful_panels) / len(successful_panels)
        print(f"   Avg response time (successful): {avg_response_time:.2f}s")
    
    # Extract session cookies for successful logins
    sessions = {}
    for result in successful_panels:
        if result['session_cookie']:
            sessions[result['name']] = result['session_cookie']
    
    print(f"   Active sessions: {len(sessions)}")
    
    return results, sessions


def example_error_handling():
    """Example demonstrating error handling."""
    print("\n🛡️ Error Handling Example")
    print("-" * 26)
    
    # Configurations with various error scenarios
    configs = [
        PanelConfig("https://httpbin.org/delay/20", "admin", "pass", "Timeout Test"),
        PanelConfig("https://invalid-domain-xyz.com", "admin", "pass", "Invalid Domain"),
        PanelConfig("https://httpbin.org/status/500", "admin", "pass", "Server Error"),
        PanelConfig("https://httpbin.org/status/200", "admin", "pass", "Success Test")
    ]
    
    # Use short timeout to demonstrate timeout handling
    automator = XUILoginAutomator(max_workers=4, timeout=3)
    results = automator.login_multiple(configs)
    
    # Analyze error types
    error_types = {}
    for result in results:
        if not result['success']:
            error_msg = result['message']
            if 'Timeout' in error_msg:
                error_types['timeout'] = error_types.get('timeout', 0) + 1
            elif 'Connection' in error_msg:
                error_types['connection'] = error_types.get('connection', 0) + 1
            elif 'HTTP' in error_msg:
                error_types['http'] = error_types.get('http', 0) + 1
            else:
                error_types['other'] = error_types.get('other', 0) + 1
    
    print(f"\n📈 Error Analysis:")
    for error_type, count in error_types.items():
        print(f"   {error_type.title()} errors: {count}")
    
    return results


def main():
    """Run all examples."""
    print("🚀 X-UI Login Automation Examples")
    print("=" * 40)
    
    try:
        # Run examples (these will fail with demo URLs, but show the functionality)
        example_basic_usage()
        example_environment_variables()
        example_single_panel()
        example_custom_processing()
        example_error_handling()
        
        print("\n✅ All examples completed!")
        print("💡 Note: Examples use demo URLs and will show connection errors.")
        print("   Replace with actual X-UI panel URLs for real testing.")
        
    except KeyboardInterrupt:
        print("\n⏹️ Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")


if __name__ == "__main__":
    main()
