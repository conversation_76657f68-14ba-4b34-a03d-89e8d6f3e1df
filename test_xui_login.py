#!/usr/bin/env python3
"""
Test script for X-UI Login Automation
Demonstrates usage and validates functionality.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import json
import tempfile
import os
from xui_login_automation import XUILogin<PERSON>utomator, PanelConfig, load_config_from_file


class Test<PERSON>UILoginAutomator(unittest.TestCase):
    """Test cases for XUILoginAutomator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.automator = XUILoginAutomator(max_workers=2, timeout=5)
        self.sample_config = PanelConfig(
            url="https://test.example.com",
            username="admin",
            password="password123",
            name="Test Panel"
        )
    
    def test_panel_config_creation(self):
        """Test PanelConfig creation and auto-naming."""
        config = PanelConfig(
            url="https://example.com",
            username="user",
            password="pass"
        )
        self.assertEqual(config.name, "https://example.com")
        
        config_with_name = PanelConfig(
            url="https://example.com",
            username="user",
            password="pass",
            name="Custom Name"
        )
        self.assertEqual(config_with_name.name, "Custom Name")
    
    @patch('requests.Session.post')
    def test_successful_login(self, mock_post):
        """Test successful login scenario."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'success': True, 'msg': 'Login successful'}
        mock_response.cookies.get_dict.return_value = {'session': 'abc123'}
        mock_post.return_value = mock_response
        
        result = self.automator.login_to_panel(self.sample_config)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['message'], 'Login successful')
        self.assertIsNotNone(result['session_cookie'])
        self.assertGreater(result['response_time'], 0)
    
    @patch('requests.Session.post')
    def test_failed_login(self, mock_post):
        """Test failed login scenario."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.reason = 'Unauthorized'
        mock_post.return_value = mock_response
        
        result = self.automator.login_to_panel(self.sample_config)
        
        self.assertFalse(result['success'])
        self.assertIn('401', result['message'])
    
    @patch('requests.Session.post')
    def test_connection_timeout(self, mock_post):
        """Test connection timeout scenario."""
        # Mock timeout exception
        mock_post.side_effect = Exception("Timeout")
        
        result = self.automator.login_to_panel(self.sample_config)
        
        self.assertFalse(result['success'])
        self.assertIn('Unexpected error', result['message'])
    
    def test_load_config_from_file(self):
        """Test loading configuration from JSON file."""
        config_data = {
            "panels": [
                {
                    "name": "Test Panel 1",
                    "url": "https://test1.com",
                    "username": "admin1",
                    "password": "pass1"
                },
                {
                    "url": "https://test2.com",
                    "username": "admin2",
                    "password": "pass2"
                }
            ]
        }
        
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_file = f.name
        
        try:
            configs = load_config_from_file(temp_file)
            
            self.assertEqual(len(configs), 2)
            self.assertEqual(configs[0].name, "Test Panel 1")
            self.assertEqual(configs[1].name, "https://test2.com")  # Auto-generated name
            self.assertEqual(configs[0].url, "https://test1.com")
            self.assertEqual(configs[1].username, "admin2")
        finally:
            os.unlink(temp_file)
    
    def test_session_creation(self):
        """Test session creation with proper headers."""
        session = self.automator.create_session()
        
        self.assertIn('User-Agent', session.headers)
        self.assertIn('Accept', session.headers)
        self.assertIn('Content-Type', session.headers)
        self.assertEqual(session.timeout, 5)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete workflow."""
    
    def test_multiple_login_workflow(self):
        """Test the complete multi-threaded login workflow."""
        configs = [
            PanelConfig("https://test1.com", "admin", "pass", "Panel 1"),
            PanelConfig("https://test2.com", "admin", "pass", "Panel 2")
        ]
        
        automator = XUILoginAutomator(max_workers=2, timeout=1)
        
        # This will fail due to invalid URLs, but tests the workflow
        results = automator.login_multiple(configs)
        
        self.assertEqual(len(results), 2)
        for result in results:
            self.assertIn('name', result)
            self.assertIn('success', result)
            self.assertIn('message', result)
            self.assertIn('response_time', result)


def run_demo():
    """Run a demonstration of the script functionality."""
    print("🧪 X-UI Login Automation Demo")
    print("=" * 50)
    
    # Create sample configurations
    demo_configs = [
        PanelConfig("https://httpbin.org/status/200", "admin", "pass", "Mock Success"),
        PanelConfig("https://httpbin.org/status/401", "admin", "pass", "Mock Failure"),
        PanelConfig("https://invalid-url-that-does-not-exist.com", "admin", "pass", "Invalid URL")
    ]
    
    print("📋 Demo configurations:")
    for config in demo_configs:
        print(f"   • {config.name}: {config.url}")
    
    print("\n🚀 Running automated login demo...")
    automator = XUILoginAutomator(max_workers=3, timeout=5)
    results = automator.login_multiple(demo_configs)
    automator.print_summary()
    
    print("\n✅ Demo completed!")
    print("💡 This demo uses mock endpoints to simulate different scenarios.")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        run_demo()
    else:
        print("Running unit tests...")
        unittest.main(verbosity=2)
