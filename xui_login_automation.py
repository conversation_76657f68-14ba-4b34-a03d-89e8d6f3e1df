#!/usr/bin/env python3
"""
X-UI Panel Automated Login Script
A clean, minimal implementation with multi-threading support and enhanced UX.
"""

import requests
import threading
import time
import json
import sys
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin
import argparse
from pathlib import Path


@dataclass
class PanelConfig:
    """Configuration for an X-UI panel."""
    url: str
    username: str
    password: str
    name: str = ""
    
    def __post_init__(self):
        if not self.name:
            self.name = self.url


class XUILoginAutomator:
    """Automated login handler for X-UI panels with multi-threading support."""
    
    def __init__(self, max_workers: int = 5, timeout: int = 10):
        self.max_workers = max_workers
        self.timeout = timeout
        self.session_timeout = 30
        self.results = []
        self.lock = threading.Lock()
        
    def create_session(self) -> requests.Session:
        """Create a configured requests session."""
        session = requests.Session()
        session.timeout = self.timeout
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json'
        })
        return session
        
    def login_to_panel(self, config: PanelConfig) -> Dict:
        """Attempt to login to a single X-UI panel."""
        result = {
            'name': config.name,
            'url': config.url,
            'success': False,
            'message': '',
            'session_cookie': None,
            'response_time': 0
        }
        
        start_time = time.time()
        session = self.create_session()
        
        try:
            # Prepare login data
            login_data = {
                'username': config.username,
                'password': config.password
            }
            
            # Attempt login
            login_url = urljoin(config.url.rstrip('/'), '/login')
            response = session.post(login_url, json=login_data, timeout=self.timeout)
            
            result['response_time'] = round(time.time() - start_time, 2)
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if response_data.get('success', False):
                        result['success'] = True
                        result['message'] = 'Login successful'
                        result['session_cookie'] = session.cookies.get_dict()
                    else:
                        result['message'] = response_data.get('msg', 'Login failed')
                except json.JSONDecodeError:
                    result['message'] = 'Invalid JSON response'
            else:
                result['message'] = f'HTTP {response.status_code}: {response.reason}'
                
        except requests.exceptions.Timeout:
            result['message'] = f'Timeout after {self.timeout}s'
        except requests.exceptions.ConnectionError:
            result['message'] = 'Connection failed'
        except requests.exceptions.RequestException as e:
            result['message'] = f'Request error: {str(e)}'
        except Exception as e:
            result['message'] = f'Unexpected error: {str(e)}'
        finally:
            session.close()
            
        return result
        
    def login_multiple(self, configs: List[PanelConfig]) -> List[Dict]:
        """Login to multiple panels concurrently."""
        self.results = []
        
        print(f"🚀 Starting login attempts for {len(configs)} panels...")
        print(f"⚙️  Using {min(self.max_workers, len(configs))} worker threads")
        print("-" * 60)
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all login tasks
            future_to_config = {
                executor.submit(self.login_to_panel, config): config 
                for config in configs
            }
            
            # Process completed tasks
            for future in as_completed(future_to_config):
                config = future_to_config[future]
                try:
                    result = future.result()
                    with self.lock:
                        self.results.append(result)
                        self._print_result(result)
                except Exception as e:
                    error_result = {
                        'name': config.name,
                        'url': config.url,
                        'success': False,
                        'message': f'Thread error: {str(e)}',
                        'session_cookie': None,
                        'response_time': 0
                    }
                    with self.lock:
                        self.results.append(error_result)
                        self._print_result(error_result)
        
        return self.results
        
    def _print_result(self, result: Dict):
        """Print a single login result with formatting."""
        status = "✅ SUCCESS" if result['success'] else "❌ FAILED"
        print(f"{status} | {result['name']:<25} | {result['response_time']:>6.2f}s | {result['message']}")
        
    def print_summary(self):
        """Print a summary of all login attempts."""
        if not self.results:
            return
            
        successful = sum(1 for r in self.results if r['success'])
        total = len(self.results)
        
        print("-" * 60)
        print(f"📊 SUMMARY: {successful}/{total} successful logins")
        
        if successful > 0:
            print(f"\n✅ Successful logins ({successful}):")
            for result in self.results:
                if result['success']:
                    print(f"   • {result['name']} ({result['response_time']}s)")
                    
        failed = total - successful
        if failed > 0:
            print(f"\n❌ Failed logins ({failed}):")
            for result in self.results:
                if not result['success']:
                    print(f"   • {result['name']}: {result['message']}")


def load_config_from_file(file_path: str) -> List[PanelConfig]:
    """Load panel configurations from a JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        configs = []
        for item in data.get('panels', []):
            config = PanelConfig(
                url=item['url'],
                username=item['username'],
                password=item['password'],
                name=item.get('name', item['url'])
            )
            configs.append(config)
            
        return configs
    except FileNotFoundError:
        print(f"❌ Config file not found: {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in config file: {e}")
        sys.exit(1)
    except KeyError as e:
        print(f"❌ Missing required field in config: {e}")
        sys.exit(1)


def create_sample_config(file_path: str):
    """Create a sample configuration file."""
    sample_config = {
        "panels": [
            {
                "name": "Main Panel",
                "url": "https://panel1.example.com",
                "username": "admin",
                "password": "password123"
            },
            {
                "name": "Backup Panel",
                "url": "https://panel2.example.com",
                "username": "admin",
                "password": "password456"
            }
        ]
    }
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2)
    
    print(f"✅ Sample config created: {file_path}")
    print("Edit the file with your actual panel details and run the script again.")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="X-UI Panel Automated Login Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python xui_login_automation.py -c panels.json
  python xui_login_automation.py -c panels.json -w 10 -t 15
  python xui_login_automation.py --create-config panels.json
        """
    )
    
    parser.add_argument('-c', '--config', default='panels.json',
                       help='Configuration file path (default: panels.json)')
    parser.add_argument('-w', '--workers', type=int, default=5,
                       help='Number of worker threads (default: 5)')
    parser.add_argument('-t', '--timeout', type=int, default=10,
                       help='Request timeout in seconds (default: 10)')
    parser.add_argument('--create-config', action='store_true',
                       help='Create a sample configuration file')
    
    args = parser.parse_args()
    
    if args.create_config:
        create_sample_config(args.config)
        return
        
    if not Path(args.config).exists():
        print(f"❌ Config file not found: {args.config}")
        print(f"💡 Create a sample config with: python {sys.argv[0]} --create-config {args.config}")
        sys.exit(1)
    
    # Load configurations
    configs = load_config_from_file(args.config)
    if not configs:
        print("❌ No panel configurations found")
        sys.exit(1)
    
    # Initialize automator and run
    automator = XUILoginAutomator(max_workers=args.workers, timeout=args.timeout)
    automator.login_multiple(configs)
    automator.print_summary()


if __name__ == "__main__":
    main()
